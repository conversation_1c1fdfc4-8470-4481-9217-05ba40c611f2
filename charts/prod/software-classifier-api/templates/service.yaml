apiVersion: v1
kind: Service
metadata:
  name: {{ include "software-classifier-api.fullname" . }}
  labels:
    {{- include "software-classifier-api.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "software-classifier-api.selectorLabels" . | nindent 4 }}
