apiVersion: v1
kind: Pod
metadata:
  name: "{{ include "batuta-gateway-hosts.fullname" . }}-test-connection"
  labels:
    {{- include "batuta-gateway-hosts.labels" . | nindent 4 }}
  annotations:
    "helm.sh/hook": test
spec:
  containers:
    - name: wget
      image: busybox
      command: ['wget']
      args: ['{{ include "batuta-gateway-hosts.fullname" . }}:{{ .Values.service.port }}']
  restartPolicy: Never
