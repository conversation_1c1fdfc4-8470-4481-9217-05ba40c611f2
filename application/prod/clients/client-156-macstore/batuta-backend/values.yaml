batuta-backend:
  client_name: client-156
  ingress:
    tls: true
    hosts: true
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod-vpn2
      alb.ingress.kubernetes.io/group.name: batuta2.prod.internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:592227762142:certificate/0135b4ea-f177-42dc-abf7-620de5971521
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:592227762142:regional/webacl/batuta-agents-prod-us-east-1-waf/85c24f8b-b104-4c49-9b8a-7848651e40c4"
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
  ingressHTTP:
    hosts: true
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod-http3
      alb.ingress.kubernetes.io/group.name: batuta.prod.http3
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:592227762142:regional/webacl/batuta-agents-prod-us-east-1-waf/85c24f8b-b104-4c49-9b8a-7848651e40c4"
      alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=3600
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
  ingressHTTPLegacy:
    enabled: false
  configmap:
    name: client-156-macstore-batuta-backend-configmap
  secretAdminDb:
    name: client-156-macstore-batuta-backend-admin-db
  secretClients:
    name: client-156-macstore-batuta-backend-client-db
  envs:
    FQDN: client-156-backend-prod.batuta.io
    FQDN_HTTP: client-156-backend-http-prod.batuta.io
    RPORT_API_KEY: client-156
    RPORT_API_PREFIX: batuta24
    SERVER_KEY_SEED: aa1213ba3841954dd939f52c17810184b05b
    MYSQL_CLIENT_DATABASE_NAME: "client-156-macstore"
    DATABASE_TYPE: mysql
    DATABASE_NAME: client-156-macstore
  cronJob:
    # first cron
    - name: cron
      env:
        - name: "CLIENT_NAME"
          value: client-156-macstore
      secret:
        name: client-156-macstore-batuta-backend-cronjob-s3
      enabled: false
      suspend: false
      schedule: "5 23 * * *"
      command: ["/bin/bash"]
      args:
        - "-c"
        - "/usr/local/bin/backup-sqlite3.sh"
      volumes:
      - name: rport
        persistentVolumeClaim:
          claimName: rport-client-156-macstore-batuta-backend-0
