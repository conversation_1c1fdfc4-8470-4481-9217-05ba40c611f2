batuta-soar:
  client_name: client-150
  ingress:
    enabled: false
  secret:
    name: client-150-cna-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-150-cna-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-150-cna-batuta-backend.client-150-cna.svc.cluster.local
    RPORT_DOMAIN_HOST: client-150-backend-http-prod.batuta.io
    RPORT_API_KEY: client-150
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
