batuta-soar:
  client_name: client-147
  ingress:
    enabled: false
  secret:
    name: client-147-rlh-properties-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-147-rlh-properties-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-147-rlh-properties-batuta-backend.client-147-rlh-properties.svc.cluster.local
    RPORT_DOMAIN_HOST: client-147-backend-http-prod.batuta.io
    RPORT_API_KEY: client-147
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
