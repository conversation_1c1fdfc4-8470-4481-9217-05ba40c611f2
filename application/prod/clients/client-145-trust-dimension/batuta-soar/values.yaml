batuta-soar:
  client_name: client-145
  ingress:
    enabled: false
  secret:
    name: client-145-trust-dimension-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-145-trust-dimension-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-145-trust-dimension-batuta-backend.client-145-trust-dimension.svc.cluster.local
    RPORT_DOMAIN_HOST: client-145-backend-http-prod.batuta.io
    RPORT_API_KEY: client-145
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
