batuta-backend:
  client_name: client-153
  ingress:
    tls: true
    hosts: true
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod-vpn2
      alb.ingress.kubernetes.io/group.name: batuta2.prod.internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTPS": 443}]'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:592227762142:certificate/0135b4ea-f177-42dc-abf7-620de5971521
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:592227762142:regional/webacl/batuta-agents-prod-us-east-1-waf/85c24f8b-b104-4c49-9b8a-7848651e40c4"
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
  ingressHTTP:
    hosts: true
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod-http3
      alb.ingress.kubernetes.io/group.name: batuta.prod.http3
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}]'
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:592227762142:regional/webacl/batuta-agents-prod-us-east-1-waf/85c24f8b-b104-4c49-9b8a-7848651e40c4"
      alb.ingress.kubernetes.io/load-balancer-attributes: idle_timeout.timeout_seconds=3600
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: http
  ingressHTTPLegacy:
    enabled: false
  configmap:
    name: client-153-intercam-batuta-backend-configmap
  secretAdminDb:
    name: client-153-intercam-batuta-backend-admin-db
  secretClients:
    name: client-153-intercam-batuta-backend-client-db
  envs:
    FQDN: client-153-backend-prod.batuta.io
    FQDN_HTTP: client-153-backend-http-prod.batuta.io
    RPORT_API_KEY: client-153
    RPORT_API_PREFIX: batuta24
    SERVER_KEY_SEED: f277e6887ca5a99dd813710ac5fc04d395b5
    MYSQL_CLIENT_DATABASE_NAME: "client-153-intercam"
    DATABASE_TYPE: mysql
    DATABASE_NAME: client-153-intercam
  cronJob:
    # first cron
    - name: cron
      env:
        - name: "CLIENT_NAME"
          value: client-153-intercam
      secret:
        name: client-153-intercam-batuta-backend-cronjob-s3
      enabled: false
      suspend: false
      schedule: "5 23 * * *"
      command: ["/bin/bash"]
      args:
        - "-c"
        - "/usr/local/bin/backup-sqlite3.sh"
      volumes:
      - name: rport
        persistentVolumeClaim:
          claimName: rport-client-153-intercam-batuta-backend-0
