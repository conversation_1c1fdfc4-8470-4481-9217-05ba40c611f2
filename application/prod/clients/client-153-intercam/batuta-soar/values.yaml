batuta-soar:
  client_name: client-153
  ingress:
    enabled: false
  secret:
    name: client-153-intercam-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-153-intercam-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-153-intercam-batuta-backend.client-153-intercam.svc.cluster.local
    RPORT_DOMAIN_HOST: client-153-backend-http-prod.batuta.io
    RPORT_API_KEY: client-153
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
