batuta-soar:
  client_name: client-157
  ingress:
    enabled: false
  secret:
    name: client-157-telcel-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-157-telcel-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-157-telcel-batuta-backend.client-157-telcel.svc.cluster.local
    RPORT_DOMAIN_HOST: client-157-backend-http-prod.batuta.io
    RPORT_API_KEY: client-157
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
