batuta-soar:
  client_name: client-143
  ingress:
    enabled: false
  secret:
    name: client-143-grupo-siayec-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-143-grupo-siayec-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-143-grupo-siayec-batuta-backend.client-143-grupo-siayec.svc.cluster.local
    RPORT_DOMAIN_HOST: client-143-backend-http-prod.batuta.io
    RPORT_API_KEY: client-143
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
