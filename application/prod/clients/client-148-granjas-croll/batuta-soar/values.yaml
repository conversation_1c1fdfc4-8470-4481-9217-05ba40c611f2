batuta-soar:
  client_name: client-148
  ingress:
    enabled: false
  secret:
    name: client-148-granjas-croll-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-148-granjas-croll-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-148-granjas-croll-batuta-backend.client-148-granjas-croll.svc.cluster.local
    RPORT_DOMAIN_HOST: client-148-backend-http-prod.batuta.io
    RPORT_API_KEY: client-148
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
