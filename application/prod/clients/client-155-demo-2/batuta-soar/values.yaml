batuta-soar:
  client_name: client-155
  ingress:
    enabled: false
  secret:
    name: client-155-demo-2-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-155-demo-2-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-155-demo-2-batuta-backend.client-155-demo-2.svc.cluster.local
    RPORT_DOMAIN_HOST: client-155-backend-http-prod.batuta.io
    RPORT_API_KEY: client-155
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
