batuta-soar:
  client_name: client-154
  ingress:
    enabled: false
  secret:
    name: client-154-demo-1-batuta-soar-doppler-token
    enabled: true
  configmap:
    name: client-154-demo-1-batuta-soar-configmap
  envs:
    RPORT_DOMAIN: client-154-demo-1-batuta-backend.client-154-demo-1.svc.cluster.local
    RPORT_DOMAIN_HOST: client-154-backend-http-prod.batuta.io
    RPORT_API_KEY: client-154
    RPORT_API_PREFIX: batuta24
    RPORT_USER: soar
