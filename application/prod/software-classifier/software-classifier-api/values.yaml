software-classifier-api:
  image:
    repository: 592227762142.dkr.ecr.us-east-1.amazonaws.com/batuta-prod-us-east-1-ecr-software-classifier
    pullPolicy: Always
    # Overrides the image tag whose default is the chart appVersion.
    tag: "software-classifier-api-281"
  replicaCount: 1
  ingress:
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod-vpn3
      alb.ingress.kubernetes.io/group.name: batuta3.prod.internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:592227762142:certificate/0135b4ea-f177-42dc-abf7-620de5971521
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
    tls:
      - hosts:
          - "sc-api.batuta.io"
    hosts:
      - host: sc-api.batuta.io
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: software-classifier-software-classifier-api
                port:
                  number: 80
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  secret:
    enabled: true
    name: software-classifier-software-classifier-api-doppler-token
  resources:
    limits:
      cpu: 2000m
      memory: 4096Mi
    requests:
      cpu: 200m
      memory: 512Mi
  tolerations:
    - key: "environment"
      effect: "NoSchedule"
      operator: "Equal"
      value: "prod"
  nodeSelector:
    Environment: "prod"
