batuta-front:
  image:
    repository: 592227762142.dkr.ecr.us-east-1.amazonaws.com/batuta-prod-us-east-1-ecr-frontend
    pullPolicy: Always
    # Overrides the image tag whose default is the chart appVersion.
    tag: "batuta-front-398"
  replicaCount: 2
  ingress:
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-prod
      alb.ingress.kubernetes.io/group.name: batuta.prod
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/ssl-redirect: "443"
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:592227762142:certificate/0135b4ea-f177-42dc-abf7-620de5971521
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:592227762142:regional/webacl/batuta-prod-us-east-1-waf/8cafa4fe-8a03-437e-ae57-f9980d709386"
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
      external-dns.alpha.kubernetes.io/hostname: manage.batuta.io
    tls:
      - hosts:
          - "manage.batuta.io"
    hosts:
      - host: manage.batuta.io
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: frontend-batuta-front
                port:
                  number: 80
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  secret:
    enabled: true
    name: frontend-batuta-front-doppler-token
  resources:
    limits:
      cpu: 1000m
      memory: 1024Mi
    requests:
      cpu: 200m
      memory: 512Mi
  autoscaling:
    enabled: false
    minReplicas: 2
    maxReplicas: 2
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  tolerations:
    - key: "environment"
      effect: "NoSchedule"
      operator: "Equal"
      value: "prod"
  nodeSelector:
    Environment: "prod"
