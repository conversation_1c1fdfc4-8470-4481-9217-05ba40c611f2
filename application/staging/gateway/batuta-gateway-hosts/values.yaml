batuta-gateway-hosts:
  image:
    repository: 021058262266.dkr.ecr.us-east-1.amazonaws.com/batuta-staging-us-east-1-ecr-gateway
    pullPolicy: Always
    # Overrides the image tag whose default is the chart appVersion.
    tag: "batuta-gateway-436"
  replicaCount: 1
  ingress:
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-stg
      alb.ingress.kubernetes.io/group.name: batuta.stg
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internet-facing
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:021058262266:certificate/13d2f2e4-a5b3-4077-86dc-f17a8bf463aa
      alb.ingress.kubernetes.io/wafv2-acl-arn: "arn:aws:wafv2:us-east-1:021058262266:regional/webacl/batuta-staging-us-east-1-waf/68d2a7f0-149f-4a81-b46c-b6efc7af97ff"
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
      external-dns.alpha.kubernetes.io/hostname: api-staging.batuta.services
    tls:
      - hosts:
          - "api-staging.batuta.services"
    hosts:
      - host: api-staging.batuta.services
        paths:
          - path: /api/gw/host/
            pathType: Prefix
            backend:
              service:
                name: gateway-batuta-gateway-hosts
                port:
                  number: 80
          - path: /api/gw/ott/
            pathType: Prefix
            backend:
              service:
                name: gateway-batuta-gateway-hosts
                port:
                  number: 80
  secret:
    enabled: true
    name: gateway-batuta-gateway-hosts-doppler-token
  resources:
    limits:
      cpu: 1000m
      memory: 4096Mi
    requests:
      cpu: 100m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 80
    targetMemoryUtilizationPercentage: 80
  tolerations:
    - key: "environment"
      effect: "NoSchedule"
      operator: "Equal"
      value: "staging"
  nodeSelector:
    Environment: "staging"
