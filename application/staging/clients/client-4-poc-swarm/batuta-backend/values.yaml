batuta-backend:
  client_name: client-4
  image:
    tag: "batuta-backend-283-debug-stg"
  configmap:
    name: client-4-poc-swarm-batuta-backend-configmap
  secretAdminDb:
    name: client-4-poc-swarm-batuta-backend-admin-db
  secretClients:
    name: client-4-poc-swarm-batuta-backend-client-db
  envs:
    FQDN: client-4-backend-staging.batuta.services
    FQDN_HTTP: client-4-backend-http-staging.batuta.services
    RPORT_API_KEY: client-4
    RPORT_API_PREFIX: batuta24
    SERVER_KEY_SEED: bad1f3d1359aedd7fdbf3cd411822ca54241
    MYSQL_CLIENT_DATABASE_NAME: "client-4-poc-swarm"
    DATABASE_TYPE: mysql
    DATABASE_NAME: client-4-poc-swarm