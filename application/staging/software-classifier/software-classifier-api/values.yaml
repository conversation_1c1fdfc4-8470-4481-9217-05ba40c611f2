software-classifier-api:
  image:
    repository: 021058262266.dkr.ecr.us-east-1.amazonaws.com/batuta-staging-us-east-1-ecr-software-classifier
    pullPolicy: Always
    # Overrides the image tag whose default is the chart appVersion.
    tag: "software-classifier-api-286"
  replicaCount: 1
  ingress:
    enabled: true
    className: alb
    annotations:
      alb.ingress.kubernetes.io/load-balancer-name: batuta-us-east-1-alb-stg-vpn1
      alb.ingress.kubernetes.io/group.name: batuta1.staging.internal
      alb.ingress.kubernetes.io/target-type: ip
      alb.ingress.kubernetes.io/scheme: internal
      alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
      alb.ingress.kubernetes.io/ssl-redirect: '443'
      alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
      alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:us-east-1:021058262266:certificate/13d2f2e4-a5b3-4077-86dc-f17a8bf463aa
      service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
      service.beta.kubernetes.io/aws-load-balancer-backend-protocol: https
    tls:
      - hosts:
          - "sc-api-staging.batuta.services"
    hosts:
      - host: sc-api-staging.batuta.services
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: software-classifier-software-classifier-api
                port:
                  number: 80
  service:
    type: ClusterIP
    port: 80
    targetPort: 80
  secret:
    enabled: true
    name: software-classifier-software-classifier-api-doppler-token
  resources:
    limits:
      cpu: 1000m
      memory: 2048Mi
    requests:
      cpu: 200m
      memory: 512Mi
  tolerations:
    - key: "environment"
      effect: "NoSchedule"
      operator: "Equal"
      value: "staging"
  nodeSelector:
    Environment: "staging"
