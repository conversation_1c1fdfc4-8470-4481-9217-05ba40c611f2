apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-clients-backend
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/clients/**/batuta-backend
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[3]}}-{{path[4]}}'
    spec:
      destination:
        namespace: '{{path[3]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[3]}}-{{path[4]}}'
          valueFiles:
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-backend.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/secrets-backend-cronjob.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/secrets-admin-db.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}//{{path[3]}}/{{path[4]}}/secrets-client-db.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-backup-backend.yaml
          - /{{path}}/values.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}/{{path[4]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-clients-soar
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/clients/**/batuta-soar
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[3]}}-{{path[4]}}'
    spec:
      destination:
        namespace: '{{path[3]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[3]}}-{{path[4]}}'
          valueFiles:
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-soar.yaml
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}/{{path[4]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-front
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/frontend/batuta-front
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-gateway
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/gateway/batuta-gateway-hosts
      - path: application/prod/gateway/batuta-gateway
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-pairing
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/pairing/batuta-pairing
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-vciso
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/vciso/batuta-vciso
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-clients-api
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/clients-api/batuta-clients-api
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-vulnerability-service
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/vulnerability-service/batuta-vulnerability-service
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-inventory-service
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/inventory-service/batuta-inventory-service
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-prod-software-classifier-api
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/prod/software-classifier/software-classifier-api
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD