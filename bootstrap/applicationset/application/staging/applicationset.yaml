apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-clients-backend
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/clients/**/batuta-backend
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[3]}}-{{path[4]}}'
    spec:
      destination:
        namespace: '{{path[3]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[3]}}-{{path[4]}}'
          valueFiles:
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-backend.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/secrets-backend-cronjob.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/secrets-admin-db.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}//{{path[3]}}/{{path[4]}}/secrets-client-db.yaml
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-backup-backend.yaml
          - /{{path}}/values.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}/{{path[4]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-clients-soar
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/clients/**/batuta-soar
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[3]}}-{{path[4]}}'
    spec:
      destination:
        namespace: '{{path[3]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[3]}}-{{path[4]}}'
          valueFiles:
          - /{{path[0]}}/{{path[1]}}/{{path[2]}}/values-soar.yaml
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}/{{path[4]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-front
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/frontend/batuta-front
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-gateway-hosts
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/gateway/batuta-gateway-hosts
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-gateway
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/gateway/batuta-gateway
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-pairing
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/pairing/batuta-pairing
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-vciso
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/vciso/batuta-vciso
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-applications
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/batuta-cloud/rabbitmq
      - path: application/staging/batuta-cloud/sonarqube
      - path: application/staging/batuta-cloud/minio
      - path: application/staging/batuta-cloud/postgresql
      - path: application/staging/batuta-cloud/redis-cluster
      - path: application/staging/batuta-cloud/batuta-cloud-api
      - path: application/staging/batuta-cloud/workflows
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: 'default'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-clients-api
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/clients-api/batuta-clients-api
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-red-staging-applications
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/batuta-red/batuta-red-backend
      - path: application/staging/batuta-red/batuta-red-farmbuilder
      - path: application/staging/batuta-red/batuta-red-farmgateway
      - path: application/staging/batuta-red/batuta-red-frontend
      - path: application/staging/batuta-red/batuta-red-go-scheduler
      - path: application/staging/batuta-red/batuta-red-mongodb
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: 'default'
      source:
        helm:
          releaseName: '{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-vulnerability-service
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/vulnerability-service/batuta-vulnerability-service
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-inventory-service
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/inventory-service/batuta-inventory-service
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-software-classifier
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/software-classifier/batuta-software-classifier
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
---
apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: batuta-staging-software-classifier-api
  namespace: argocd
spec:
  generators:
  - git:
      directories:
      - path: application/staging/software-classifier/software-classifier-api
      repoURL: **************:Humanapis/batuta-deployments.git
      revision: HEAD
  template:
    metadata:
      name: '{{path[2]}}-{{path[3]}}'
    spec:
      destination:
        namespace: '{{path[2]}}'
        server: https://kubernetes.default.svc
      syncPolicy:
        automated:
          selfHeal: true
          prune: true
        syncOptions:
        - CreateNamespace=true
      project: '{{path[1]}}'
      source:
        helm:
          releaseName: '{{path[2]}}-{{path[3]}}'
          valueFiles:
          - /{{path}}/values.yaml
          - /{{path}}/secrets.yaml
        path: '{{path[0]}}/{{path[1]}}/{{path[2]}}/{{path[3]}}'
        repoURL: **************:Humanapis/batuta-deployments.git
        targetRevision: HEAD
